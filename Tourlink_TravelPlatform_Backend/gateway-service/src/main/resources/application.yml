server:
  port: 9082
spring:
  application:
    name: gateway-service
  cloud:
    gateway:
      routes:
        - id: attraction-service
          uri: http://localhost:9080
          predicates:
            - Path=/attraction/**
          filters:
            - StripPrefix=1
        - id: data-platform-service
          uri: http://localhost:9081
          predicates:
            - Path=/data-platform/**
          filters:
            - StripPrefix=1
        - id: routing-service
          uri: http://localhost:9083
          predicates:
            - Path=/routing/**
          filters:
            - StripPrefix=1
        - id: social-service
          uri: http://localhost:9084
          predicates:
            - Path=/social/**
          filters:
            - StripPrefix=1
        - id: user-service
          uri: http://localhost:9085
          predicates:
            - Path=/user/**
          filters:
            - StripPrefix=1
      discovery:
        locator:
          enabled: true  # 开启通过服务名自动转发
          lower-case-service-id: true