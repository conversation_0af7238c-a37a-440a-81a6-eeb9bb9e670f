server:
  port: 9080
spring:
  application:
    name: attraction-service
  datasource:
    url: jdbc:mysql://*************:3306/tl_attraction_service?useSSL=false&serverTimezone=UTC&connectTimeout=60000&socketTimeout=60000&allowPublicKeyRetrieval=true
    username: root
    password: TourLink@2025
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 60000  # 连接超时时间，60秒
      maximum-pool-size: 10      # 最大连接数
      minimum-idle: 5            # 最小空闲连接
      idle-timeout: 600000       # 空闲连接超时时间，10分钟
      max-lifetime: 1800000      # 连接最大生命周期，30分钟
      validation-timeout: 3000   # 验证查询超时时间，3秒
      connection-test-query: SELECT 1  # 连接测试查询
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        # 优化批处理操作
        jdbc.batch_size: 50
        order_inserts: true
        order_updates: true
    open-in-view: false
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848