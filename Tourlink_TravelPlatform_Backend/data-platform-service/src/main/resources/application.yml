server:
  port: 9081
spring:
  application:
    name: data-platform-service
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
    openfeign:
      client:
        config:
          default:
            connect-timeout: 5000
            read-timeout: 8000
          attraction-service:
            url: http://attraction-service:9080
          social-service:
            url: http://social-service:9084
  datasource:
    url: jdbc:mysql://*************:3306/tl_data_platform_service?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: root
    password: TourLink@2025
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      idle-timeout: 30000          # 空闲连接最大存活时间
      max-lifetime: 600000         # 连接最大生命周期
      connection-timeout: 30000    # 获取连接的超时时间
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update